<?php
/**
 * Main Index Page
 * MLM Binary Plan System
 */

// Enable error reporting in development
if (!defined('ENVIRONMENT') || ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Include essential files
require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/session.php';
require_once 'includes/functions.php';
require_once 'includes/FileUpload.php';

// Check if user is already logged in and redirect accordingly
if (isLoggedIn()) {
    $userType = getCurrentUserType();
    switch ($userType) {
        case 'admin':
            header("Location: admin/dashboard.php");
            break;
        case 'franchise':
            header("Location: franchise/dashboard.php");
            break;
        case 'user':
            header("Location: user/dashboard.php");
            break;
        default:
            destroyUserSession();
            break;
    }
    exit();
}

// Get featured products for homepage
$db = Database::getInstance();
$productsStmt = $db->prepare("SELECT * FROM products WHERE status = 'active' ORDER BY created_at DESC LIMIT 6");
$productsStmt->execute();
$featuredProducts = $productsStmt->fetchAll();

$fileUpload = new FileUpload();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome To Shakti Pure Industries Pvt Ltd</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-teal: #0891b2;
            --primary-green: #059669;
            --light-green: #d1fae5;
            --dark-green: #047857;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --white: #ffffff;
            --light-gray: #f8fafc;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background-color: var(--light-gray);
        }

        /* Top Header */
        .top-header {
            background: var(--primary-teal);
            color: white;
            padding: 8px 0;
            font-size: 14px;
        }

        .top-header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .top-header-left {
            font-weight: 500;
        }

        .top-header-right {
            display: flex;
            gap: 20px;
        }

        .top-header-right a {
            color: white;
            text-decoration: none;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .top-header-right a:hover {
            opacity: 0.8;
        }

        /* Main Header */
        .main-header {
            background: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo img {
            height: 50px;
            margin-right: 10px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-green);
        }

        .logo-tagline {
            font-size: 12px;
            color: var(--text-light);
            font-style: italic;
        }

        .search-bar {
            flex: 1;
            max-width: 400px;
            margin: 0 30px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 50px 12px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
        }

        .search-input:focus {
            border-color: var(--primary-teal);
        }

        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--primary-teal);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
        }

        .cart-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .cart-btn {
            background: var(--primary-green);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Navigation */
        .navigation {
            background: var(--primary-green);
            padding: 0;
        }

        .nav-content {
            display: flex;
            align-items: center;
        }

        .categories-btn {
            background: var(--dark-green);
            color: white;
            padding: 15px 25px;
            border: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
            cursor: pointer;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            flex: 1;
        }

        .nav-menu li {
            margin: 0;
        }

        .nav-menu a {
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            display: block;
            font-weight: 500;
            transition: background 0.3s ease;
        }

        .nav-menu a:hover {
            background: rgba(255,255,255,0.1);
        }

        /* Hero Banner */
        .hero-banner {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            padding: 40px 0;
            margin: 20px 0;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
        }

        .banner-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .banner-text {
            flex: 1;
            padding-right: 40px;
        }

        .banner-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 15px;
            line-height: 1.2;
        }

        .banner-subtitle {
            font-size: 1.2rem;
            color: var(--primary-green);
            font-weight: 600;
            margin-bottom: 20px;
        }

        .banner-features {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .banner-features li {
            margin-bottom: 8px;
            color: var(--text-dark);
            font-weight: 500;
        }

        .banner-features li::before {
            content: '>';
            color: var(--primary-green);
            font-weight: bold;
            margin-right: 8px;
        }

        .banner-image {
            flex: 1;
            text-align: center;
        }

        .banner-image img {
            max-width: 100%;
            height: auto;
        }

        /* Best Selling Section */
        .best-selling {
            padding: 40px 0;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 30px;
            text-align: center;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .product-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .product-discount {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #ef4444;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            z-index: 2;
        }

        .product-image {
            height: 200px;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .product-image img {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }

        .product-info {
            padding: 15px;
        }

        .product-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .product-price {
            font-size: 16px;
            font-weight: 700;
            color: var(--primary-green);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .search-bar {
                margin: 0;
                max-width: 100%;
            }

            .nav-content {
                flex-direction: column;
            }

            .categories-btn {
                width: 100%;
                justify-content: center;
            }

            .nav-menu {
                width: 100%;
                flex-direction: column;
            }

            .banner-content {
                flex-direction: column;
                text-align: center;
            }

            .banner-text {
                padding-right: 0;
                margin-bottom: 20px;
            }

            .banner-title {
                font-size: 2rem;
            }

            .product-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }
        }

        @media (max-width: 576px) {
            .top-header {
                display: none;
            }

            .banner-title {
                font-size: 1.5rem;
            }

            .product-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- Top Header -->
    <div class="top-header">
        <div class="container">
            <div class="top-header-left">
                Welcome To Shakti Pure Industries Pvt Ltd
            </div>
            <div class="top-header-right">
                <a href="user/register.php"><i class="fas fa-user"></i> Register</a>
                <a href="user/login.php"><i class="fas fa-sign-in-alt"></i> Sign In</a>
            </div>
        </div>
    </div>

    <!-- Main Header -->
    <header class="main-header">
        <div class="container">
            <div class="header-content">
                <!-- Logo -->
                <a href="index.php" class="logo">
                    <div style="display: flex; align-items: center;">
                        <div style="display: flex; align-items: center; justify-content: center; margin-right: 10px;">
                            <span style="color: white; font-weight: bold; font-size: 20px;"></span>
                        </div>
                        <div>
                            <img src="assets/images/logo.png" alt="">
                        </div>
                    </div>
                </a>

                <!-- Search Bar -->
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="Search Product...">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>

                <!-- Cart Info -->
                <div class="cart-info">
                    <a href="#" class="cart-btn">
                        <i class="fas fa-shopping-cart"></i>
                        SHOPPING CART<br>
                        <small>(Products) - ₹</small>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navigation">
        <div class="container">
            <div class="nav-content">
                <button class="categories-btn">
                    <i class="fas fa-bars"></i>
                    PRODUCT CATEGORIES
                </button>
                <ul class="nav-menu">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="#">Shop</a></li>
                    <li><a href="#">About Us</a></li>
                    <li><a href="#">Business Opportunity</a></li>
                    <li><a href="#">Contact Us</a></li>
                    <li><a href="user/register.php">Register</a></li>
                    <li><a href="user/login.php">Login</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Banner -->
    <section class="hero-banner">
        <div class="container">
<div class="slider-container">
    <div class="slider">
        <!-- Slide 1 -->
        <div class="slide">
            <div class="banner-content">
                <div class="banner-text">
                    <div class="banner-subtitle">Secure your family with</div>
                    <h1 class="banner-title">Shaktipure gas safety device</h1>
                    <div class="quality-section">
                        <strong>Quality of product:</strong>
                    </div>
                    <ul class="banner-features">
                        <li>Cuts major Gas Leaks.</li>
                        <li>Facilitates in detecting gas leakage in system.</li>
                        <li>Indicates low level of LP gas in Cylinder's.</li>
                        <li>25 years life cycle.</li>
                        <li>5 year Warranty.</li>
                        <li>Save upto 25% Gas.</li>
                        <li>Liability Insurances 25 Cr.</li>
                    </ul>
                    <div class="cta-section">
                        <h3>Protect your family & property from gas accidents</h3>
                        <div class="cta-button">GAS SAFE DEVICE</div>
                    </div>
                </div>
                <div class="banner-image">
                    <img src="assets/images/gas-safety-banner.svg" alt="Gas Safety Device">
                </div>
            </div>
        </div>

        <!-- Slide 2 -->
        <div class="slide">
            <div class="banner-content">
                <div class="banner-text">
                    <div class="banner-subtitle">Advanced Technology</div>
                    <h1 class="banner-title">Smart Gas Detection System</h1>
                    <ul class="banner-features">
                        <li>24/7 Real-time Monitoring</li>
                        <li>Automatic Shutdown System</li>
                        <li>Mobile App Integration</li>
                        <li>Smart Alerts & Notifications</li>
                    </ul>
                    <div class="cta-section">
                        <h3>Experience Next-Gen Safety Solutions</h3>
                        <div class="cta-button">LEARN MORE</div>
                    </div>
                </div>
                <div class="banner-image">
                    <img src="assets/images/smart-detection.svg" alt="Smart Detection">
                </div>
            </div>
        </div>

        <!-- Slide 3 -->
        <div class="slide">
            <div class="banner-content">
                <div class="banner-text">
                    <div class="banner-subtitle">Industry Leading</div>
                    <h1 class="banner-title">Commercial Safety Solutions</h1>
                    <ul class="banner-features">
                        <li>Industrial Grade Equipment</li>
                        <li>Customizable Solutions</li>
                        <li>Professional Installation</li>
                        <li>24/7 Support</li>
                    </ul>
                    <div class="cta-section">
                        <h3>Secure Your Business Today</h3>
                        <div class="cta-button">GET STARTED</div>
                    </div>
                </div>
                <div class="banner-image">
                    <img src="assets/images/commercial-safety.svg" alt="Commercial Safety">
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Buttons -->
    <button class="slider-nav prev"><i class="fas fa-chevron-left"></i></button>
    <button class="slider-nav next"><i class="fas fa-chevron-right"></i></button>

    <!-- Dots Navigation -->
    <div class="slider-dots"></div>
</div>

<style>
.slider-container {
    position: relative;
    overflow: hidden;
    width: 100%;
}

.slider {
    display: flex;
    transition: transform 0.5s ease-in-out;
}

.slide {
    min-width: 100%;
    padding: 20px;
}

.banner-content {
    display: flex;
    align-items: center;
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
}

.banner-text {
    flex: 1;
}

.banner-subtitle {
    color: var(--primary-green);
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.banner-title {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.quality-section {
    margin: 20px 0;
}

.quality-section strong {
    color: var(--primary-green);
    font-size: 1.1rem;
}

.banner-features {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.banner-features li {
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
}

.banner-features li:before {
    content: '→';
    position: absolute;
    left: 0;
    color: var(--primary-green);
}

.cta-section {
    margin-top: 30px;
}

.cta-section h3 {
    color: var(--primary-green);
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.cta-button {
    background: var(--primary-green);
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    display: inline-block;
    font-weight: bold;
    font-size: 1.2rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.cta-button:hover {
    background: var(--dark-green);
}

.banner-image {
    flex: 1;
    text-align: center;
}

.banner-image img {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
}

.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.8);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.slider-nav:hover {
    background: white;
}

.prev {
    left: 20px;
}

.next {
    right: 20px;
}

.slider-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: background 0.3s ease;
}

.dot.active {
    background: white;
}

@media (max-width: 992px) {
    .banner-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .banner-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .banner-features {
        text-align: left;
    }

    .banner-title {
        font-size: 1.8rem;
    }

    .slider-nav {
        width: 30px;
        height: 30px;
    }
}

@media (max-width: 576px) {
    .banner-title {
        font-size: 1.5rem;
    }

    .cta-button {
        padding: 12px 24px;
        font-size: 1rem;
    }

    .slider-nav {
        display: none;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.querySelector('.slider');
    const slides = document.querySelectorAll('.slide');
    const prevBtn = document.querySelector('.prev');
    const nextBtn = document.querySelector('.next');
    const dotsContainer = document.querySelector('.slider-dots');

    let currentSlide = 0;
    const slideCount = slides.length;

    // Create dots
    slides.forEach((_, index) => {
        const dot = document.createElement('div');
        dot.classList.add('dot');
        if (index === 0) dot.classList.add('active');
        dot.addEventListener('click', () => goToSlide(index));
        dotsContainer.appendChild(dot);
    });

    const dots = document.querySelectorAll('.dot');

    function updateDots() {
        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === currentSlide);
        });
    }

    function goToSlide(n) {
        currentSlide = n;
        slider.style.transform = `translateX(-${currentSlide * 100}%)`;
        updateDots();
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % slideCount;
        goToSlide(currentSlide);
    }

    function prevSlide() {
        currentSlide = (currentSlide - 1 + slideCount) % slideCount;
        goToSlide(currentSlide);
    }

    // Event listeners
    prevBtn.addEventListener('click', prevSlide);
    nextBtn.addEventListener('click', nextSlide);

    // Touch events for mobile
    let touchStartX = 0;
    let touchEndX = 0;

    slider.addEventListener('touchstart', e => {
        touchStartX = e.changedTouches[0].screenX;
    });

    slider.addEventListener('touchend', e => {
        touchEndX = e.changedTouches[0].screenX;
        if (touchStartX - touchEndX > 50) {
            nextSlide();
        } else if (touchEndX - touchStartX > 50) {
            prevSlide();
        }
    });

    // Auto slide
    let slideInterval = setInterval(nextSlide, 5000);

    // Pause auto slide on hover
    slider.addEventListener('mouseenter', () => {
        clearInterval(slideInterval);
    });

    slider.addEventListener('mouseleave', () => {
        slideInterval = setInterval(nextSlide, 5000);
    });
});
</script>
        </div>
    </section>

    <!-- Best Selling Products -->
    <section class="best-selling">
        <div class="container">
            <h2 class="section-title">BEST SELLING PRODUCT</h2>

            <div class="product-grid">
                <?php if (!empty($featuredProducts)): ?>
                    <?php foreach ($featuredProducts as $index => $product): ?>
                        <div class="product-card">
                            <?php
                            // Add discount badges for variety
                            $discounts = ['-33%', '-25%', '-36%', '-16%'];
                            $discount = $discounts[$index % count($discounts)];
                            ?>
                            <div class="product-discount"><?php echo $discount; ?></div>

                            <div class="product-image">
                                <?php if ($product['image']): ?>
                                    <img src="<?php echo htmlspecialchars($fileUpload->getFileUrl($product['image'])); ?>"
                                         alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php else: ?>
                                    <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #d1fae5, #a7f3d0); display: flex; align-items: center; justify-content: center; color: #059669; font-size: 24px; font-weight: bold;">
                                        <?php echo strtoupper(substr($product['name'], 0, 2)); ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="product-info">
                                <div class="product-title"><?php echo htmlspecialchars($product['name']); ?></div>
                                <div class="product-price">₹<?php echo number_format($product['price'], 2); ?></div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Sample products if no database products -->
                    <div class="product-card">
                        <div class="product-discount">-33%</div>
                        <div class="product-image">
                            <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #dbeafe, #bfdbfe); display: flex; align-items: center; justify-content: center; color: #0891b2; font-size: 24px; font-weight: bold;">
                                GS
                            </div>
                        </div>
                        <div class="product-info">
                            <div class="product-title">Gas Safety Device</div>
                            <div class="product-price">₹2,500.00</div>
                        </div>
                    </div>

                    <div class="product-card">
                        <div class="product-discount">-25%</div>
                        <div class="product-image">
                            <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #d1fae5, #a7f3d0); display: flex; align-items: center; justify-content: center; color: #059669; font-size: 24px; font-weight: bold;">
                                SM
                            </div>
                        </div>
                        <div class="product-info">
                            <div class="product-title">Safety Monitor</div>
                            <div class="product-price">₹1,800.00</div>
                        </div>
                    </div>

                    <div class="product-card">
                        <div class="product-discount">-36%</div>
                        <div class="product-image">
                            <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #fef3c7, #fde68a); display: flex; align-items: center; justify-content: center; color: #d97706; font-size: 24px; font-weight: bold;">
                                GL
                            </div>
                        </div>
                        <div class="product-info">
                            <div class="product-title">Gas Leak Detector</div>
                            <div class="product-price">₹3,200.00</div>
                        </div>
                    </div>

                    <div class="product-card">
                        <div class="product-discount">-16%</div>
                        <div class="product-image">
                            <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #fce7f3, #fbcfe8); display: flex; align-items: center; justify-content: center; color: #be185d; font-size: 24px; font-weight: bold;">
                                SC
                            </div>
                        </div>
                        <div class="product-info">
                            <div class="product-title">Safety Controller</div>
                            <div class="product-price">₹2,100.00</div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>



    <!-- Footer -->
    <footer style="background: #2c3e50; color: white; padding: 50px 0 20px;">
        <div class="container">
            <div class="row">
                <!-- My Account Section -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5 style="color: white; margin-bottom: 20px; font-weight: 600;">MY ACCOUNT</h5>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">My Account</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">Register</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">Login</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">Franchisee/Store Login</a></li>
                    </ul>
                </div>

                <!-- Customer Service Section -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5 style="color: white; margin-bottom: 20px; font-weight: 600;">CUSTOMER SERVICE</h5>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">Privacy Policy</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">Terms & Conditions</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">Shipping, Return, Refund and Cancellation policy</a></li>
                    </ul>
                </div>

                <!-- Quick Links Section -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5 style="color: white; margin-bottom: 20px; font-weight: 600;">QUICK LINKS</h5>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">About Us</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">Register</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #3498db; text-decoration: none; font-size: 14px;">Login</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">Contact Us</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">Legal Document</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">Awards</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">Our Banker</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">Gallery</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #bdc3c7; text-decoration: none; font-size: 14px;">Download</a></li>
                    </ul>
                </div>

                <!-- Contact Us Section -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5 style="color: white; margin-bottom: 20px; font-weight: 600;">CONTACT US</h5>
                    <div style="margin-bottom: 15px;">
                        <i class="fas fa-map-marker-alt" style="color: #3498db; margin-right: 10px;"></i>
                        <span style="color: #bdc3c7; font-size: 14px; line-height: 1.6;">
                            Shakti Pure Industries Pvt Ltd, D-224,<br>
                            Udhana Complex, Udhana, Surat,<br>
                            394210, Gujarat, India
                        </span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <i class="fas fa-envelope" style="color: #3498db; margin-right: 10px;"></i>
                        <span style="color: #bdc3c7; font-size: 14px;"><EMAIL></span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <i class="fas fa-phone" style="color: #3498db; margin-right: 10px;"></i>
                        <span style="color: #bdc3c7; font-size: 14px;">+91 8460403679</span>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <i class="fas fa-phone" style="color: #3498db; margin-right: 10px;"></i>
                        <span style="color: #bdc3c7; font-size: 14px;">+91 9879044051</span>
                    </div>
                </div>
            </div>

            <!-- Payment Methods and Social Media -->
            <div class="row" style="border-top: 1px solid #34495e; padding-top: 30px; margin-top: 30px;">
                <div class="col-md-6 mb-3">
                    <h6 style="color: white; margin-bottom: 15px; font-weight: 600;">ACCEPTED</h6>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <img src="assets/images/paytm-logo.svg" alt="PayTM" style="height: 30px; border-radius: 4px;">
                        <img src="assets/images/mastercard-logo.svg" alt="MasterCard" style="height: 30px; border-radius: 4px;">
                        <img src="assets/images/visa-logo.svg" alt="Visa" style="height: 30px; border-radius: 4px;">
                    </div>
                </div>
                <div class="col-md-6 mb-3 text-md-end">
                    <h6 style="color: white; margin-bottom: 15px; font-weight: 600;">FOLLOW US</h6>
                    <div style="display: flex; justify-content: flex-end; gap: 10px;">
                        <a href="#" style="width: 35px; height: 35px; background: #3b5998; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-decoration: none;">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" style="width: 35px; height: 35px; background: #1da1f2; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-decoration: none;">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" style="width: 35px; height: 35px; background: #dc2626; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-decoration: none;">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="#" style="width: 35px; height: 35px; background: #e4405f; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-decoration: none;">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Copyright Section -->
            <div class="row" style="border-top: 1px solid #34495e; padding-top: 20px; margin-top: 20px;">
                <div class="col-md-6">
                    <p style="margin: 0; color: #bdc3c7; font-size: 13px;">&copy; 2025 Shakti Pure Industries Pvt Ltd all Rights Reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p style="margin: 0; color: #bdc3c7; font-size: 13px;">Website Design and Developed by : Shakti Pure Industries Pvt Ltd</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Search functionality
        document.querySelector('.search-btn')?.addEventListener('click', function() {
            const searchInput = document.querySelector('.search-input');
            const searchTerm = searchInput.value.trim();
            if (searchTerm) {
                // Redirect to products page with search term
                window.location.href = `products.php?search=${encodeURIComponent(searchTerm)}`;
            }
        });

        // Enter key search
        document.querySelector('.search-input')?.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.querySelector('.search-btn').click();
            }
        });

        // Product card click handlers
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', function() {
                // Get product info from the card
                const productTitle = this.querySelector('.product-title')?.textContent;
                if (productTitle) {
                    // Redirect to product detail page
                    window.location.href = `product-detail.php?name=${encodeURIComponent(productTitle)}`;
                }
            });

            // Add hover effect
            card.style.cursor = 'pointer';
        });

        // Categories button toggle
        document.querySelector('.categories-btn')?.addEventListener('click', function() {
            alert('Product categories feature coming soon!');
        });

        // Mobile responsive menu
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                document.querySelector('.nav-menu').style.display = 'flex';
            } else {
                document.querySelector('.nav-menu').style.display = 'none';
            }
        });

        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add to cart functionality (placeholder)
        function addToCart(productId) {
            alert('Add to cart functionality will be implemented soon!');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('ShaktiPure website loaded successfully!');
        });
    </script>
</body>
</html>
